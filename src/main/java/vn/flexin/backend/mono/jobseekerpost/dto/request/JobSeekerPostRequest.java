package vn.flexin.backend.mono.jobseekerpost.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.JobSeekerExperienceDto;
import vn.flexin.backend.mono.post.dto.SalaryDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobSeekerPostRequest {
    private String industry;
    private Long jobSeekerId;
    private String title;
    private String description;
    private String jobType;
    private String contractType;
    private String workType;
    private AddressRequest location;
    private List<JobSeekerExperienceDto> experiences;
    private SalaryDto salary;
    private Set<String> skills;
    private Set<String> languages;
    private String educationLevel;
    private String educationDetail;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer workingHourPerDay;
    private Set<String> workingDays;
    private Set<String> workingShifts;
    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;
    @JsonProperty("isEnableEmailNotification")
    private boolean isEnableEmailNotification;
    @JsonProperty("isEnableInformation")
    private boolean isEnableInformation;
    @JsonProperty("isAutoAcceptInterviewInvitation")
    private boolean isAutoAcceptInterviewInvitation;
    @JsonProperty("isReady")
    private boolean isReady;
    private Integer featureDuration;
    private String status;
}
