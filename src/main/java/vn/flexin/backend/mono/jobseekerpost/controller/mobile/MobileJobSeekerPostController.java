package vn.flexin.backend.mono.jobseekerpost.controller.mobile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.jobseekerpost.dto.JobSeekerPostFilter;
import vn.flexin.backend.mono.jobseekerpost.dto.request.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.request.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerDetailResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerPostOverViewResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.SearchJobSeekerPostResponse;

import java.util.List;

@Tag(name = "Mobile Posts for Job-seeker APIs", description = "Mobile post for job-seeker endpoints")
@RequestMapping("/v1/mobile/job-seeker-posts")
public interface MobileJobSeekerPostController {

    @Operation(summary = "Search list posts of job-seeker", description = "Search list posts of job-seeker")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker posts fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/search")
    ResponseEntity<PaginationApiResponseDto<List<SearchJobSeekerPostResponse>>> searchPostForJobSeeker(@Valid @RequestBody JobSeekerPostFilter filters);

    @Operation(summary = "Create new post", description = "Create new post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post created successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createPost(@RequestBody CreateJobSeekerPostRequest request);

    @Operation(summary = "Update post", description = "Update post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PutMapping("/{id}")
    ResponseEntity<ApiResponseDto<Boolean>> updatePost(@PathVariable("id") Long id,
                                                       @Validated @RequestBody UpdateJobSeekerPostRequest request);

    @Operation(summary = "Get detail post", description = "Get detail post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/{id}")
    ResponseEntity<ApiResponseDto<JobSeekerDetailResponse>> getDetailPost(@PathVariable("id") Long id);

    @Operation(summary = "Delete post", description = "Delete post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @DeleteMapping("/{id}")
    ResponseEntity<ApiResponseDto<Boolean>> deletePost(@PathVariable("id") Long id);

    @Operation(summary = "Toggle post", description = "Toggle post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post toggled successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{id}/toggle-featured")
    ResponseEntity<ApiResponseDto<Boolean>> togglePost(@PathVariable("id") Long id);

    @Operation(summary = "Close post", description = "Close post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post closed successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{id}/status")
    ResponseEntity<ApiResponseDto<Boolean>> updateStatus(@PathVariable("id") Long id,
                                                         @RequestParam("status") String status);


    @Operation(summary = "Duplicate post", description = "Duplicate post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post duplicate successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{id}/duplicate")
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> duplicatePost(@PathVariable("id") Long id);

    @Operation(summary = "Get post statistic", description = "Get post statistic")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post statistics fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/{id}/statistics")
    ResponseEntity<ApiResponseDto<JobSeekerStatisticPostResponse>> getPostStatistic(@PathVariable("id") Long id);

    @Operation(summary = "Get post overview of current user", description = "Get post overview of current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Overview post fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/me/over-view")
    ResponseEntity<ApiResponseDto<JobSeekerPostOverViewResponse>> getPostOverview();


    @Operation(summary = "Close post", description = "Close post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "job-seeker post closed successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{id}/view")
    ResponseEntity<ApiResponseDto<Boolean>> viewPost(@PathVariable("id") Long id);

}

