package vn.flexin.backend.mono.jobseekerpost.service.admin;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.JobSeekerPostFilter;
import vn.flexin.backend.mono.jobseekerpost.dto.request.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.request.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerDetailResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.SearchJobSeekerPostResponse;
import vn.flexin.backend.mono.jobseekerpost.service.JobSeekerPostService;

import java.util.List;

@Service
@RequiredArgsConstructor
public class AdminJobSeekerPostServiceImpl implements AdminJobSeekerPostService {
    private final JobSeekerPostService jobSeekerPostService;

    @Override
    public CreateObjectResponse createPost(CreateJobSeekerPostRequest request) {
        return jobSeekerPostService.createPost(request);
    }

    @Override
    public void updatePost(UpdateJobSeekerPostRequest request) {
        jobSeekerPostService.updatePost(request);
    }

    @Override
    public JobSeekerDetailResponse getDetailPost(Long id) {
        return jobSeekerPostService.getDetailPost(id);
    }

    @Override
    public void deletePost(Long id) {
        jobSeekerPostService.deletePost(id);
    }

    @Override
    public JobSeekerStatisticPostResponse getStatistics() {
        // TODO: Implement admin-specific statistics if needed
        return new JobSeekerStatisticPostResponse();
    }

    @Override
    public List<SearchJobSeekerPostResponse> searchJobSeekerPost(JobSeekerPostFilter filters) {
        return jobSeekerPostService.searchJobSeekerPost(filters).getLeft();
    }
} 