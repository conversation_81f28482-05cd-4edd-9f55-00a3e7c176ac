package vn.flexin.backend.mono.jobseekerpost.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.*;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.jobseekerpost.entity.JobSeekerPost;
import vn.flexin.backend.mono.post.enums.PostStatus;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobSeekerPostFilter extends BaseFilter<JobSeekerPost> {
    private Long jobSeekerId;
    private String keyword;
    private String status;
    private List<String> skills;


    @Override
    public Specification<JobSeekerPost> toSpecification() {
        var condition = new Condition();
        if(jobSeekerId != null){
            condition.append(new Join(JobSeekerPost.Fields.jobSeeker, List.of(new Where(User.Fields.id, jobSeekerId))));
        }

        if(skills != null){
            condition.append(new Where(JobSeekerPost.Fields.skills, Operator.IN, skills));
        }

        if (keyword != null) {
            var subDesCondition = new Condition().append(new Where(JobSeekerPost.Fields.description, Operator.LIKE_IGNORE_CASE, keyword));
            var subTitleCondition = new Condition().append(new Where(JobSeekerPost.Fields.title, Operator.LIKE_IGNORE_CASE, keyword));
            condition.appendComplex(new Where(Complex.OR, List.of(subTitleCondition, subDesCondition)));
        }

        if (status != null) {
            condition.append(new Where(JobSeekerPost.Fields.postStatus, Operator.EQUAL, PostStatus.fromString(status)));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}
