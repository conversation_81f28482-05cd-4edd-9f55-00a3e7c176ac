package vn.flexin.backend.mono.jobseekerpost.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.response.SimpleAddressResponse;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchJobSeekerPostResponse {
    private Long id;
    private Long jobSeekerId;
    private String status;
    private Set<String> skills;
    private String title;
    private String description;
    private Set<String> workingDays;
    private SimpleAddressResponse location;
    private Integer opportunityCount;
    private Integer newOpportunityCount;
    private Integer viewCount;
    private boolean isFeatureJob;
    private Integer featureDuration;
    private Set<String> workingShifts;
    private Integer interviewRequestCount;
}
