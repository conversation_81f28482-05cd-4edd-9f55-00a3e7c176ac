package vn.flexin.backend.mono.jobseekerpost.service;

import org.apache.commons.lang3.tuple.Pair;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.JobSeekerPostFilter;
import vn.flexin.backend.mono.jobseekerpost.dto.request.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.request.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerDetailResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerPostOverViewResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.SearchJobSeekerPostResponse;
import vn.flexin.backend.mono.post.dto.SearchPostFilter;

import java.util.List;

public interface JobSeekerPostService {
    CreateObjectResponse createPost(CreateJobSeekerPostRequest request);

    void updatePost(UpdateJobSeekerPostRequest request);

    JobSeekerDetailResponse getDetailPost(Long id);

    void deletePost(Long id);

    void updateStatusPost(Long id, String status);

    void viewPost(Long id);

    CreateObjectResponse duplicatePost(Long id);

    JobSeekerStatisticPostResponse getPostStatistic(Long id);

    public Pair<List<SearchJobSeekerPostResponse>, PaginationResponse> searchJobSeekerPost(JobSeekerPostFilter filters) ;

    JobSeekerPostOverViewResponse getPostOverview();
}
