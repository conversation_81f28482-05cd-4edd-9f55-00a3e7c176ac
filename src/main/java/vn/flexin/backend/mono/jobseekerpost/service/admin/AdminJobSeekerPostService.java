package vn.flexin.backend.mono.jobseekerpost.service.admin;

import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.JobSeekerPostFilter;
import vn.flexin.backend.mono.jobseekerpost.dto.request.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.request.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerDetailResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.SearchJobSeekerPostResponse;

import java.util.List;

public interface AdminJobSeekerPostService {
    CreateObjectResponse createPost(CreateJobSeekerPostRequest request);
    void updatePost(UpdateJobSeekerPostRequest request);
    JobSeekerDetailResponse getDetailPost(Long id);
    void deletePost(Long id);
    JobSeekerStatisticPostResponse getStatistics();
    List<SearchJobSeekerPostResponse> searchJobSeekerPost(JobSeekerPostFilter filters);
} 