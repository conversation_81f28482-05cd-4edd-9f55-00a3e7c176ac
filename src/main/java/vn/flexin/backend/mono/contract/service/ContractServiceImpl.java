package vn.flexin.backend.mono.contract.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.contract.dto.*;
import vn.flexin.backend.mono.contract.entity.Contract;
import vn.flexin.backend.mono.contract.entity.ContractMessage;
import vn.flexin.backend.mono.contract.entity.PaymentRecord;
import vn.flexin.backend.mono.contract.entity.TimeEntry;
import vn.flexin.backend.mono.contract.repository.ContractMessageRepository;
import vn.flexin.backend.mono.contract.repository.ContractRepository;
import vn.flexin.backend.mono.contract.repository.PaymentRecordRepository;
import vn.flexin.backend.mono.contract.repository.TimeEntryRepository;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ContractServiceImpl implements ContractService {

    private final ContractRepository contractRepository;
    private final TimeEntryRepository timeEntryRepository;
    private final ContractMessageRepository messageRepository;
    private final PaymentRecordRepository paymentRecordRepository;
    private final UserService userService;

    @Override
    public ContractDetailResponse createContract(ContractRequet requet) {
        log.info("Creating new contract: {}", requet);

        User employer = userService.getUserEntityById(requet.getEmployerId());
        User jobSeeker = userService.getUserEntityById(requet.getJobSeekerId());

        Contract contract = new Contract();
        contract.setTitle(requet.getTitle());
        contract.setEmployer(employer);
        contract.setJobSeeker(jobSeeker);
        contract.setStartDate(requet.getStartDate());
        contract.setEndDate(requet.getEndDate());
        contract.setHourlyRate(requet.getHourlyRate());
        contract.setPaymentFrequency(requet.getPaymentFrequency());
        contract.setWorkingHoursPerWeek(requet.getWorkingHoursPerWeek());
        contract.setWorkDays(requet.getWorkDays());
        contract.setContractType(requet.getContractType());
        contract.setStatus(requet.getStatus());
        contract.setAdditionalTerms(requet.getAdditionalTerms());

        Contract savedContract = contractRepository.save(contract);
        log.info("Contract created successfully with ID: {}", savedContract.getId());

        return mapDettailResponse(savedContract);
    }

    @Override
    public ContractDetailResponse getContractById(Long id) {
        log.info("Fetching contract with ID: {}", id);
        Contract contract = getContractEntityById(id);
        return mapDettailResponse(contract);
    }

    @Override
    public List<ContractDetailResponse> getAllContracts() {
        log.info("Fetching all contracts");
        List<Contract> contracts = contractRepository.findAll();
        return contracts.stream().map(this::mapDettailResponse).collect(Collectors.toList());
    }

    @Override
    public List<ContractDetailResponse> getContractsByEmployerId(Long employerId) {
        log.info("Fetching contracts for employer ID: {}", employerId);
        User employer = userService.getUserEntityById(employerId);
        List<Contract> contracts = contractRepository.findByEmployer(employer);
        return contracts.stream().map(this::mapDettailResponse).collect(Collectors.toList());
    }

    @Override
    public List<ContractDetailResponse> getContractsByFreelancerId(Long freelancerId) {
        log.info("Fetching contracts for freelancer ID: {}", freelancerId);
        User freelancer = userService.getUserEntityById(freelancerId);
        List<Contract> contracts = contractRepository.findByJobSeeker(freelancer);
        return contracts.stream().map(this::mapDettailResponse).collect(Collectors.toList());
    }

    @Override
    public List<ContractDetailResponse> getContractsByEmployerIdAndStatus(Long employerId, String status) {
        log.info("Fetching contracts for employer ID: {} with status: {}", employerId, status);
        User employer = userService.getUserEntityById(employerId);
        List<Contract> contracts = contractRepository.findByEmployerAndStatus(employer, status);
        return contracts.stream().map(this::mapDettailResponse).collect(Collectors.toList());
    }

    @Override
    public List<ContractDetailResponse> getContractsByFreelancerIdAndStatus(Long freelancerId, String status) {
        log.info("Fetching contracts for freelancer ID: {} with status: {}", freelancerId, status);
        User freelancer = userService.getUserEntityById(freelancerId);
        List<Contract> contracts = contractRepository.findByJobSeekerAndStatus(freelancer, status);
        return contracts.stream().map(this::mapDettailResponse).collect(Collectors.toList());
    }

    @Override
    public ContractDetailResponse updateContract(Long id, ContractRequet requet) {
        log.info("Updating contract with ID: {}", id);
        Contract contract = getContractEntityById(id);

        contract.setTitle(requet.getTitle());
        contract.setStartDate(requet.getStartDate());
        contract.setEndDate(requet.getEndDate());
        contract.setHourlyRate(requet.getHourlyRate());
        contract.setPaymentFrequency(requet.getPaymentFrequency());
        contract.setWorkingHoursPerWeek(requet.getWorkingHoursPerWeek());
        contract.setWorkDays(requet.getWorkDays());
        contract.setContractType(requet.getContractType());
        contract.setAdditionalTerms(requet.getAdditionalTerms());

        Contract updatedContract = contractRepository.save(contract);
        log.info("Contract updated successfully");

        return mapDettailResponse(updatedContract);
    }

    @Override
    public ContractDetailResponse updateContractStatus(Long id, String status, String notes) {
        log.info("Updating status of contract with ID: {} to: {}", id, status);
        Contract contract = getContractEntityById(id);

        contract.setStatus(status);

        if (status.equals("active")) {
            contract.setActivatedAt(LocalDateTime.now());
        } else if (status.equals("terminated")) {
            contract.setTerminatedAt(LocalDateTime.now());
            contract.setTerminationReason(notes);
        }

        Contract updatedContract = contractRepository.save(contract);
        log.info("Contract status updated successfully");

        return mapDettailResponse(updatedContract);
    }

    @Override
    public Boolean deleteContract(Long id) {
        log.info("Deleting contract with ID: {}", id);
        Contract contract = getContractEntityById(id);
        contractRepository.delete(contract);
        log.info("Contract deleted successfully");
        return Boolean.TRUE;
    }

    @Override
    public Contract getContractEntityById(Long id) {
        return contractRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("Contract", "id", id));
    }

    private ContractDetailResponse mapDettailResponse(Contract contract) {
        ContractDetailResponse contractDetailResponse = new ContractDetailResponse();
        contractDetailResponse.setId(contract.getId());
        contractDetailResponse.setTitle(contract.getTitle());
        contractDetailResponse.setEmployerId(contract.getEmployer().getId());
        contractDetailResponse.setEmployerName(contract.getEmployer().getName());
        contractDetailResponse.setJobSeekerId(contract.getJobSeeker().getId());
        contractDetailResponse.setJobSeekerName(contract.getJobSeeker().getName());
        contractDetailResponse.setStartDate(contract.getStartDate());
        contractDetailResponse.setEndDate(contract.getEndDate());
        contractDetailResponse.setHourlyRate(contract.getHourlyRate());
        contractDetailResponse.setPaymentFrequency(contract.getPaymentFrequency());
        contractDetailResponse.setWorkingHoursPerWeek(contract.getWorkingHoursPerWeek());
        contractDetailResponse.setWorkDays(contract.getWorkDays());
        contractDetailResponse.setContractType(contract.getContractType());
        contractDetailResponse.setStatus(contract.getStatus());
        contractDetailResponse.setActivatedAt(contract.getActivatedAt());
        contractDetailResponse.setTerminatedAt(contract.getTerminatedAt());
        contractDetailResponse.setTerminationReason(contract.getTerminationReason());
        contractDetailResponse.setFeePaid(contract.isFeePaid());
        contractDetailResponse.setAdditionalTerms(contract.getAdditionalTerms());
        contractDetailResponse.setTimeEntries(contract.getTimeEntries().stream().map(TimeEntry::toResponse).collect(Collectors.toList()));
        contractDetailResponse.setPayments(contract.getPayments().stream().map(PaymentRecord::toResponse).collect(Collectors.toSet()));
        contractDetailResponse.setMessages(contract.getMessages().stream().map(ContractMessage::toResponse).collect(Collectors.toList()));
        return contractDetailResponse;
    }

    @Override
    public Pair<List<ContractResponse>, PaginationResponse> searchContracts(ContractFilter filter) {
        var contracts = contractRepository.findAll(filter);
        List<ContractResponse> responses = contracts.stream().map(Contract::toResponse).toList();
        PaginationResponse paging = new PaginationResponse(filter.getLimit(), filter.getPage(), (int) contracts.getTotalElements());
        return Pair.of(responses, paging);
    }

    @Override
    public Pair<List<TimeEntryResponse>, PaginationResponse> searchTimeEntry(TimeEntryFilter filter) {
        var timeEntries = timeEntryRepository.findAll(filter);
        List<TimeEntryResponse> responses = timeEntries.stream().map(TimeEntry::toResponse).toList();
        PaginationResponse paging = new PaginationResponse(filter.getLimit(), filter.getPage(), (int) timeEntries.getTotalElements());
        return Pair.of(responses, paging);
    }

    @Override
    public TimeEntryResponse createTimeEntry(TimeEntryRequest request) {
        var contract = getContractEntityById(request.getContractId());
        var timeEntry = request.toEntity();
        timeEntry.setContract(contract);
        return timeEntryRepository.save(timeEntry).toResponse();
    }

    @Override
    public TimeEntryResponse updateTimeEntryStatus(TimeEntryRequest request) {
        if (StringUtils.isEmpty(request.getStatus())) {
            return null;
        }
        getContractEntityById(request.getContractId());
        var timeEntry = timeEntryRepository.findById(request.getId()).orElseThrow(() -> new ResourceNotFoundException("TimeEntry", "id", request.getId()));
        timeEntry.setStatus(request.getStatus());
        return timeEntryRepository.save(timeEntry).toResponse();
    }

    @Override
    public Pair<List<PaymentRecordResponse>, PaginationResponse> searchPaymentRecord(PaymentRecordFilter filter) {
        var paymentRecords = paymentRecordRepository.findAll(filter);
        var responses = paymentRecords.stream().map(PaymentRecord::toResponse).toList();
        PaginationResponse paging = new PaginationResponse(filter.getLimit(), filter.getPage(), (int) paymentRecords.getTotalElements());
        return Pair.of(responses, paging);
    }

    @Override
    public PaymentRecordResponse createPaymentRecord(PaymentRecordRequest request) {
        var contract = getContractEntityById(request.getContractId());
        var payment = request.toEntity();
        payment.setContract(contract);
        return paymentRecordRepository.save(payment).toResponse();
    }

    @Override
    public PaymentRecordResponse updatePaymentRecordStatus(PaymentRecordRequest request) {
        getContractEntityById(request.getContractId());
        var payment = paymentRecordRepository.findById(request.getId()).orElseThrow(() -> new ResourceNotFoundException("PaymentRecord", "id", request.getId()));
        payment.setStatus(request.getStatus());
        payment.setTransactionId(request.getTransactionId());
        return paymentRecordRepository.save(payment).toResponse();
    }

    @Override
    public Pair<List<ContractMessageResponse>, PaginationResponse> searchMessage(ContractMessageFilter filter) {
        var messages = messageRepository.findAll(filter);
        var responses = messages.stream().map(ContractMessage::toResponse).toList();
        PaginationResponse paging = new PaginationResponse(filter.getLimit(), filter.getPage(), (int) messages.getTotalElements());
        return Pair.of(responses, paging);
    }

    @Override
    public ContractMessageResponse updateMessageIsRead(ContractMessageRequest request) {
        getContractEntityById(request.getContractId());
        var message = messageRepository.findById(request.getId()).orElseThrow(() -> new ResourceNotFoundException("Message", "id", request.getId()));
        message.setRead(true);
        return messageRepository.save(message).toResponse();
    }

} 