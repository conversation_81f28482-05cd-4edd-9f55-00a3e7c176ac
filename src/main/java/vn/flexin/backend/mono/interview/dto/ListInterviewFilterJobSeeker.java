package vn.flexin.backend.mono.interview.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.Constant;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.interview.entity.Interview;
import vn.flexin.backend.mono.post.entity.Post;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListInterviewFilterJobSeeker extends BaseFilter<Interview> {

    private List<String> status;
    private String type;
    private Long jobSeekerId;
    private Long jobPostId;
    private LocalDateTime startDateRange;
    private LocalDateTime endDateRange;

    public String getSortBy() {
        return StringUtils.isEmpty(sortBy) ? "createdAt" : sortBy;
    }

    @Override
    public String getSortOrder() {
        return StringUtils.isEmpty(sortOrder) ? Constant.SORT_DESC : sortOrder;
    }

    @Override
    public Specification<Interview> toSpecification() {
        var condition = new Condition()
                .append(new Join(Interview.Fields.jobSeeker, List.of(new Where(User.Fields.id, jobSeekerId))))
                .append(new Join(Interview.Fields.post, List.of(new Where(Post.Fields.id, jobPostId))))
                .append(new Where(Interview.Fields.status, Operator.IN, status))
                .append(new Where(Interview.Fields.scheduledTime, Operator.GREATER_THAN_OR_EQUAL, LocalDateTime.of(startDateRange.toLocalDate(), LocalTime.MIN)))
                .append(new Where(Interview.Fields.scheduledTime, Operator.LESS_THAN_OR_EQUAL, LocalDateTime.of(endDateRange.toLocalDate(), LocalTime.MAX)));
        return SpecificationUtil.bySearchQuery(condition);
    }
}