package vn.flexin.backend.mono.interview.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import vn.flexin.backend.mono.application.dto.InterviewResponse;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.post.entity.Post;
import vn.flexin.backend.mono.user.entity.User;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "t_interviews")
@EntityListeners(AuditingEntityListener.class)
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class Interview extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employer_id", nullable = false)
    private User employer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "job_seeker_id", nullable = false)
    private User jobSeeker;

    @Column(nullable = false)
    private LocalDateTime scheduledTime;

    private Integer durationMinutes = 30;

    @Column(nullable = false)
    private String status = "scheduled"; // scheduled, in_progress, completed, cancelled

    private String meetingLink;

    private String meetingId;

    private String meetingCode;

    private String meetingPassword;

    @Column(columnDefinition = "TEXT")
    private String notes;

    @Column(columnDefinition = "TEXT")
    private String reason;

    private LocalDateTime startedAt;

    private LocalDateTime endedAt;

    private String cancellationReason;

    @Column(columnDefinition = "TEXT")
    private String feedback;

    private Integer employerRating;

    private Integer jobSeekerRating;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "post_id")
    private Post post;

    @Column(nullable = false)
    private String cancelFrom; // job-seeker, employer

    public InterviewResponse toResponse() {
        InterviewResponse response = new InterviewResponse();
        response.setId(id);
        response.setEmployerId(employer.getId());
        response.setEmployerName(employer.getName());
        response.setJobSeekerId(jobSeeker.getId());
        response.setJobSeekerName(jobSeeker.getName());
        response.setScheduledTime(scheduledTime);
        response.setDurationMinutes(durationMinutes);
        response.setStatus(status);
        response.setMeetingLink(meetingLink);
        response.setCancellationReason(cancellationReason);
        response.setFeedback(feedback);
        response.setEmployerRating(employerRating);
        response.setJobSeekerRating(jobSeekerRating);
        response.setNotes(notes);
        response.setStartedAt(startedAt);
        response.setEndedAt(endedAt);
        response.setMeetingPassword(meetingPassword);
        response.setMeetingId(meetingId);
        response.setMeetingId(meetingCode);
        response.setCreatedAt(createdAt);
        response.setCreatedBy(createdBy);
        response.setLastModifiedAt(lastModifiedAt);
        response.setLastModifiedBy(lastModifiedBy);
        return response;
    }
} 