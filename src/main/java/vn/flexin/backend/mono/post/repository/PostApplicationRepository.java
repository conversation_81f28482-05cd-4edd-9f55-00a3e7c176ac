package vn.flexin.backend.mono.post.repository;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.post.entity.PostApplication;

import java.util.List;
import java.util.Optional;

public interface PostApplicationRepository extends JpaSpecificationRepository<PostApplication, Long> {
    @Query(value = """
        SELECT pa
        FROM PostApplication pa
        WHERE pa.post.id = :postId
    """)
    List<PostApplication> findPostApplicationByPostId(@Param("postId") Long postId);

    @Query(value = """
        SELECT pa
        FROM PostApplication pa
        WHERE pa.resume.user.id = :user_id
        AND pa.post.id = :postId
    """)
    Optional<PostApplication> findPostApplicationByUserIdAndPostId(@Param("user_id") Long user_id, @Param("postId") Long postId);

    @Modifying
    @Query("DELETE FROM PostApplication pi WHERE pi.post.id = :id")
    void deleteByPostId(@Param("id") Long id);

    @Query(value = """
        SELECT pa
        FROM PostApplication pa
        WHERE pa.post.id = :postId
    """)
    List<PostApplication> findAllByPostId(@Param("postId") Long postId);
}
