package vn.flexin.backend.mono.post.service.impl;

import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.post.dto.SalaryDto;
import vn.flexin.backend.mono.post.entity.Salary;
import vn.flexin.backend.mono.post.repository.SalaryRepository;
import vn.flexin.backend.mono.post.service.SalaryService;

@Service
public class SalaryServiceImpl implements SalaryService {

    private final SalaryRepository salaryRepository;

    public SalaryServiceImpl(SalaryRepository salaryRepository) {
        this.salaryRepository = salaryRepository;
    }

    @Override
    public Salary createSalary(SalaryDto dto) {
        Salary salary = ModelMapperUtils.toObject(dto, Salary.class);
        return save(salary);
    }

    @Override
    public Salary save(Salary salary) {
        return salaryRepository.save(salary);
    }

    @Override
    public void updateSalary(Salary salary, SalaryDto request) {
        salary.setMin(request.getMin());
        salary.setMax(request.getMax());
        salary.setCurrency(request.getCurrency());
        salary.setPeriod(request.getPeriod());
        save(salary);
    }
}
