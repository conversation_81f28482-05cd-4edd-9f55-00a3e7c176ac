package vn.flexin.backend.mono.post.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
public class SearchPostRequest {
    @NotNull
    private Long employerId;
    private Integer page;
    private Integer limit;
    private String search;
    private SearchPostFilter filter;

    public SearchPostRequest(Long employerId, Integer page, Integer limit, String search, SearchPostFilter filter) {
        this.employerId = employerId;
        this.page = page == null ? 1 : page;
        this.limit = limit == null ? 1 : limit;
        this.search = search;
        this.filter = filter;
    }
}
