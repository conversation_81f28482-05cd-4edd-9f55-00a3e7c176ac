package vn.flexin.backend.mono.post.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)

public class SearchAdminPostResponse {
    private Long id;
    private String title;
    private String description;
    private String location;
    private String companyName;
    private SalaryDto salary;
    private String status;
    private LocalDateTime postDate;
    private LocalDateTime expireDate;
    private int applicationCount;
    private int newApplications;
    private List<PostWorkingInformation>  workingInformation;
    private String workType;
    private ExperienceDto experience;
    private String positions;
    @JsonProperty("urgentHiring")
    private boolean urgentHiring;
    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;
    private String jobType;
    private List<String> skills;
    private long viewCount;
    private List<String> benefits;
    private List<PostRequiredDocument> requiredDocuments;
    private String rejectionReason;
    private String experienceLevel;
    private Long companyId;
    private Long branchId;
    private String branchName;
}
