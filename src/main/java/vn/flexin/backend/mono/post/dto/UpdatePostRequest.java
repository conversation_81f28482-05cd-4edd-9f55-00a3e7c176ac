package vn.flexin.backend.mono.post.dto;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class UpdatePostRequest {
    @Hidden
    private Long id;

    @NotBlank
    private String title;

    private String description;

    @Valid
    @NotNull
    SalaryDto salary;

    private List<PostWorkingInformation> workingInformation;
    private String workType;

    public List<PostWorkingInformation> getWorkingInformation() { return workingInformation; }
    public String getWorkType() { return workType; }
}
