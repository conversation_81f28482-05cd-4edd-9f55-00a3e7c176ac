package vn.flexin.backend.mono.post.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ForbiddenException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.lookup.entity.Lookup;
import vn.flexin.backend.mono.lookup.enums.LookupType;
import vn.flexin.backend.mono.lookup.service.LookupService;
import vn.flexin.backend.mono.post.dto.*;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.response.PostApplicationResponse;
import vn.flexin.backend.mono.post.entity.*;
import vn.flexin.backend.mono.post.enums.PostStatus;
import vn.flexin.backend.mono.post.repository.*;
import vn.flexin.backend.mono.post.service.PostService;
import vn.flexin.backend.mono.post.service.PostViewService;
import vn.flexin.backend.mono.post.service.SalaryService;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static vn.flexin.backend.mono.post.enums.PostApplicationStatus.*;

@Service
@AllArgsConstructor
@Slf4j
@Transactional
public class PostServiceImpl implements PostService {

    private final PostViewRepository postViewRepository;
    private final PostRepository postRepository;
    private final UserService userService;
    private final SalaryService salaryService;
    private final LookupService lookupService;
    private final PostViewService postViewService;
    private final PostInterestRepository postInterestRepository;
    private final PostApplicationRepository postApplicationRepository;

    @Override
    public Pair<List<SearchPostResponse>, PaginationResponse> searchPostForEmployer(SearchPostFilter filters) {
        var posts = postRepository.findAll(filters);
        List<SearchPostResponse> responses = posts.stream().map(this::toSearchPostResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getLimit(), filters.getPage(), (int) posts.getTotalElements());
        return Pair.of(responses, paging);
    }

    private SearchPostResponse toSearchPostResponse(Post post) {
        SearchPostResponse response = ModelMapperUtils.toObject(post, SearchPostResponse.class);
        response.setEmployerId(post.getEmployer().getId());
        response.setSalary(new SalaryDto(post.getSalary()));
        setListSkills(response, post);
        if (post.getExperience() != null) {
            response.setExperience(new ExperienceDto(post.getExperience()));
        }
        response.setApplicantCount(CollectionUtils.isEmpty(post.getPostApplication()) ? 0 : post.getPostApplication().size());
        response.setViewCount(post.getPostViewCount());
        response.setWorkingInformation(post.getWorkingInformation());
        response.setWorkType(post.getWorkType());
        return response;
    }

    private void setListSkills(SearchPostResponse response, Post post) {
        List<String> skills = new ArrayList<>();
        if(!CollectionUtils.isEmpty(post.getSkills())) {
            skills = post.getSkills().stream().map(Lookup::getValue).toList();
        }
        response.setSkills(skills);
    }

    @Override
    public CreateObjectResponse createEmployerPost(CreatePostRequest request) {
        Post post = ModelMapperUtils.toObject(request, Post.class);

        User currentLoginUser = userService.getCurrentLoginUser();

        post.setEmployer(currentLoginUser);

        Salary salary = salaryService.createSalary(request.getSalary());
        post.setSalary(salary);

        List<Lookup> skills = createPostSkills(request.getSkills());
        post.setSkills(skills);

        Map<String, Object> experience = createPostExperience(request.getExperience());
        post.setExperience(experience);

        post.setStatus(PostStatus.ACTIVE.getValue());
        post.setClosedAt(null);
        post.setWorkingInformation(request.getWorkingInformation());
        post.setWorkType(request.getWorkType());

        save(post);

        return new CreateObjectResponse(post.getId());
    }

    private Map<String, Object> createPostExperience(ExperienceDto experience) {
        Map<String, Object> result = new HashMap<>();
        result.put("min", experience.getMin());
        result.put("max", experience.getMax());
        return result;
    }

    @Override
    public Post save(Post post) {
        return postRepository.save(post);
    }

    @Override
    public void updateEmployerPost(UpdatePostRequest request) {
        Post post = getById(request.getId());

        validatePermissionUser(post.getEmployer());

        Salary salary = post.getSalary();
        salaryService.updateSalary(salary, request.getSalary());

        post.setTitle(request.getTitle());
        post.setDescription(request.getDescription());
        post.setWorkingInformation(request.getWorkingInformation());
        post.setWorkType(request.getWorkType());

        save(post);
    }

    @Override
    public Post getById(Long id) {
        return postRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("Post not found."));
    }

    @Override
    public DetailPostResponse getDetailPost(Long id) {
        Post post = getById(id);
        postViewService.createPostView(id);
        return toDetailPostResponse(post);
    }

    private DetailPostResponse toDetailPostResponse (Post post) {
        DetailPostResponse response = ModelMapperUtils.toObject(post, DetailPostResponse.class);

        setSalaryResponse(response, post);
        setExperienceResponse(response, post);
        setSkillResponse(response, post);
        response.setViewCount(post.getPostViewCount());
        response.setApplicantCount(CollectionUtils.isEmpty(post.getPostApplication()) ? 0 : post.getPostApplication().size());
        response.setEmployerId(post.getEmployer().getId());
        response.setWorkingInformation(post.getWorkingInformation());
        response.setWorkType(post.getWorkType());
        response.setExperienceLevel(post.getExperienceLevel());

        return response;
    }

    @Override
    public void deletePost(Long id) {
        Post post = getById(id);
        validatePermissionUser(post.getEmployer());
        postViewRepository.deleteByPostId(id);
        postInterestRepository.deleteByPostId(id);
        postApplicationRepository.deleteByPostId(id);
        postRepository.delete(post);
    }

    private void validatePermissionUser(User employer) {
        User user = userService.getCurrentLoginUser();
        if (!Objects.equals(user.getId(), employer.getId())) {
            throw new ForbiddenException();
        }
    }

    @Override
    public void togglePost(Long id) {
        Post post = getById(id);
        validatePermissionUser(post.getEmployer());

        post.setFeatureJob(!post.isFeatureJob());

        save(post);
    }

    @Override
    public void updateStatusPost(Long id, PostStatus postStatus) {
        Post post = getById(id);
        validatePermissionUser(post.getEmployer());

        post.setStatus(postStatus.getValue());

        save(post);
    }

    @Override
    public DetailPostResponse duplicatePost(Long id) {
        User user = userService.getCurrentLoginUser();
        Post post = getById(id);

        if (!Objects.equals(user.getId(), post.getEmployer().getId())) {
            throw new ForbiddenException();
        }
        Post duplicatedPost = new Post();
        duplicatedPost.setTitle(post.getTitle());
        duplicatedPost.setDescription(post.getDescription());
        duplicatedPost.setLocation(post.getLocation());
        duplicatedPost.setSkills(duplicateSkills(post.getSkills()));
        duplicatedPost.setExperience(post.getExperience());
        duplicatedPost.setJobType(post.getJobType());
        duplicatedPost.setRemote(post.isRemote());
        duplicatedPost.setEducation(post.getEducation());
        duplicatedPost.setFeatureJob(post.isFeatureJob());
        duplicatedPost.setClosedAt(null);
        duplicatedPost.setStatus(PostStatus.ACTIVE.getValue());
        duplicatedPost.setPostViews(Collections.emptyList());
        duplicatedPost.setEmployer(user);
        duplicatedPost.setSalary(post.getSalary());
        duplicatedPost.setWorkingInformation(post.getWorkingInformation());
        duplicatedPost.setWorkType(post.getWorkType());

        duplicatedPost = save(duplicatedPost);

        return toDetailPostResponse(duplicatedPost);
    }

    private List<Lookup> duplicateSkills(List<Lookup> skills) {
        if (CollectionUtils.isEmpty(skills)) {
            return Collections.emptyList();
        }
        return new ArrayList<>(skills);
    }

    @Override
    public StatisticPostResponse getPostStatistic(Long id) {
        Post post = getByIdForStatistic(id);
        User user = userService.getCurrentLoginUser();

        if (!Objects.equals(user.getId(), post.getEmployer().getId())) {
            throw new ForbiddenException();
        }

        List<PostView> views = post.getPostViews();
        List<PostApplication> applicants = post.getPostApplication();
        StatisticPostResponse response = new StatisticPostResponse();
        response.setId(post.getId());
        setDataStatisticViews(response, views);
        setDataStatisticApplicants(response, applicants);
        return response;
    }

    @Override
    public Pair<List<SearchPostResponse>, PaginationResponse> getInterestedPosts(PostInterestFilter filters) {
        User user = userService.getCurrentLoginUser();
        var posts = postRepository.findPostsInterestedByUserId(user.getId());
        List<SearchPostResponse> responses = posts.stream().map(this::toSearchPostResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getPage(), filters.getLimit(), posts.size());
        return Pair.of(responses, paging);
    }

    @Override
    public Pair<List<SearchPostResponse>, PaginationResponse> getAppliedPosts(PostApplicationFilter filters) {
        User user = userService.getCurrentLoginUser();
        var posts = postRepository.findPostsApplicationByUserId(user.getId());
        List<SearchPostResponse> responses = posts.stream().map(this::toSearchPostResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getPage(), filters.getLimit(), posts.size());
        return Pair.of(responses, paging);
    }

    private PostApplicationResponse toPostApplicationResponse(PostApplication postApplication){
        return new PostApplicationResponse(postApplication);
    }

    @Override
    public Pair<List<PostApplicationResponse>, PaginationResponse> getPostApplications(Long postId, PostApplicationFilter filters) {
        var postApplications = postApplicationRepository.findPostApplicationByPostId(postId);
        List<PostApplicationResponse> responses = postApplications.stream().map(this::toPostApplicationResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getPage(), filters.getLimit(), postApplications.size());

        return Pair.of(responses, paging);
    }

    private void setDataStatisticApplicants(StatisticPostResponse response, List<PostApplication> applicants) {
        int shortlisted = 0;
        int rejected = 0;
        int hired = 0;
        Map<LocalDate, Integer> applicantsByDayMap = new HashMap<>();

        for (PostApplication applicant : applicants) {
            switch (applicant.getStatus()) {
                case "short_list":
                    shortlisted++;
                    break;
                case "reject":
                    rejected++;
                    break;
                case "hire":
                    hired++;
                    break;
            }
            countByDate(applicantsByDayMap, applicant.getCreatedAt());
        }
        List<StatisticPostResponse.CountResponse> applicationsPerDays = new ArrayList<>();
        applicantsByDayMap.forEach((date, count) -> applicationsPerDays.add(new StatisticPostResponse.CountResponse(date.toString(), count)));
        response.setHired(hired);
        response.setRejected(rejected);
        response.setShortlisted(shortlisted);
        response.setApplications(applicants.size());
        response.setApplicationsPerDay(applicationsPerDays);
        response.setConversionRate((double) response.getApplications() /(response.getViews() == 0 ? 1 : response.getViews()));
    }

    private void countByDate(Map<LocalDate, Integer> applicantsByDayMap, LocalDateTime createdAt) {
        LocalDate viewDate = createdAt.toLocalDate();
        if(CollectionUtils.isEmpty(applicantsByDayMap.keySet()) || !applicantsByDayMap.containsKey(viewDate)) {
            applicantsByDayMap.put(viewDate, 1);
        } else {
            Integer viewCount = applicantsByDayMap.get(viewDate);
            applicantsByDayMap.replace(viewDate, viewCount, viewCount+1);
        }
    }

    private void setDataStatisticViews(StatisticPostResponse response, List<PostView> views) {
        Map<LocalDate, Integer> viewsByDayMap = new HashMap<>();
        for (PostView view : views) {
            countByDate(viewsByDayMap, view.getCreatedAt());
        }
        List<StatisticPostResponse.CountResponse> viewPerDays = new ArrayList<>();
        viewsByDayMap.forEach((date, count) -> viewPerDays.add(new StatisticPostResponse.CountResponse(date.toString(), count)));
        response.setViews(views.size());
        response.setViewsPerDay(viewPerDays);
    }

    private Post getByIdForStatistic(Long id) {
        return postRepository.findByIdForStatistic(id).orElseThrow(() -> new ResourceNotFoundException("Post not found."));
    }

    private void setSkillResponse(DetailPostResponse response, Post post) {
        if (CollectionUtils.isEmpty(post.getSkills())) {
            response.setSkills(Collections.emptyList());
        }
        List<String> skills = post.getSkills().stream().map(Lookup::getValue).toList();
        response.setSkills(skills);
    }

    private void setExperienceResponse(DetailPostResponse response, Post post) {
        ExperienceDto experience = ModelMapperUtils.toObject(post.getExperience(), ExperienceDto.class);
        response.setExperience(experience);
    }

    private void setSalaryResponse(DetailPostResponse response, Post post) {
        SalaryDto dto = ModelMapperUtils.toObject(post.getSalary(), SalaryDto.class);
        response.setSalary(dto);
    }

    private List<Lookup> createPostSkills(List<String> skills) {
        List<Lookup> skillLookups = lookupService.getAllByType(LookupType.SKILL.getValue());
        List<Lookup> results = new ArrayList<>();
        skills.forEach(skill -> {
            Lookup lookup = skillLookups.stream().filter(item -> item.getValue().equals(skill)).findFirst().orElse(null);
            if (lookup != null) {
                results.add(lookup);
            }
        });
        return results;
    }

}
