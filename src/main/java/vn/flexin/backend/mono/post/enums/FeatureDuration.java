package vn.flexin.backend.mono.post.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum FeatureDuration {
    THREE_DAY("THREE_DAY", 3, 50),
    FIVE_DAY("FIVE_DAY", 5, 80),
    SEVEN_DAY("SEVEN_DAY", 7, 100),
    TEN_DAY("TEN_DAY", 10, 150);

    private final String value;
    private final Integer day;
    private final Integer redeemPoint;

    FeatureDuration(String value, Integer day, Integer redeemPoint) {
        this.value = value;
        this.day = day;
        this.redeemPoint = redeemPoint;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.value).toList());
    }

    @JsonCreator
    public static FeatureDuration fromString(String value) {
        try {
            return valueOf(value);
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Feature duration must be any of [" + getValues() +
                    "]");
        }
    }
}
