package vn.flexin.backend.mono.post.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class DetailPostResponse {
    private Long id;
    private Long employerId;
    private String title;
    private String description;
    private String location;
    private SalaryDto salary;
    private String jobType;
    @JsonProperty("isRemote")
    private boolean isRemote;
    private List<String> skills;
    private ExperienceDto experience;
    private String education;
    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime closedAt;
    private int applicantCount;
    private long viewCount;
    private List<PostWorkingInformation> workingInformation;
    private String workType;
    private String experienceLevel;
}
