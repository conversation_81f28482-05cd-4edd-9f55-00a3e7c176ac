package vn.flexin.backend.mono.post.repository.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.post.dto.SearchAdminPostRequest;
import vn.flexin.backend.mono.post.dto.SearchPostFilter;
import vn.flexin.backend.mono.post.dto.SearchPostRequest;
import vn.flexin.backend.mono.post.entity.Post;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Repository
public class PostRepositoryCustomize {

    @PersistenceContext
    private EntityManager entityManager;

    @Transactional(readOnly = true)
    public List<Post> searchPost(SearchPostRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Post> query = cb.createQuery(Post.class);
        Root<Post> postRoot = query.from(Post.class);

        List<Predicate> predicates = new ArrayList<>();

        buildSearchPostPredicates(request, predicates, postRoot, cb);

        // Build the final query
        query.select(postRoot).where(predicates.toArray(new Predicate[0]));

        // Pagination
        if (request.getPage() != null && request.getLimit() != null) {
            int offset = (request.getPage() - 1) * request.getLimit();
            return entityManager.createQuery(query)
                    .setFirstResult(offset)
                    .setMaxResults(request.getLimit())
                    .getResultList();
        } else {
            return entityManager.createQuery(query).getResultList();
        }
    }

    @Transactional(readOnly = true)
    public long countPosts(SearchPostRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Post> postRoot = countQuery.from(Post.class);

        // Using the count function
        countQuery.select(cb.count(postRoot));

        List<Predicate> predicates = new ArrayList<>();

        buildSearchPostPredicates(request, predicates, postRoot, cb);

        // Apply predicates to the count query
        countQuery.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private void buildSearchPostPredicates(SearchPostRequest request, List<Predicate> predicates, Root<Post> postRoot, CriteriaBuilder cb) {
        if (request.getEmployerId() != null) {
            predicates.add(cb.equal(postRoot.get("employer").get("id"), request.getEmployerId()));
        }

        if (request.getSearch() != null && !request.getSearch().trim().isEmpty()) {
            Predicate titlePredicate = cb.like(postRoot.get("title"), "%" + request.getSearch() + "%");
            Predicate desPredicate = cb.like(postRoot.get("description"), "%" + request.getSearch() + "%");
            titlePredicate = cb.or(titlePredicate, desPredicate);
            predicates.add(titlePredicate);
        }

        if (request.getFilter() == null) {
            return;
        }

        SearchPostFilter filter = request.getFilter();

        if (!CollectionUtils.isEmpty(filter.getJobType())) {
            predicates.add(postRoot.get("jobType").in(filter.getJobType()));
        }

        predicates.add(cb.equal(postRoot.get("isRemote"), filter.getIsRemote()));

        if (filter.getSalary() != null) {
            if (filter.getSalary().getMin() != null) {
                predicates.add(cb.greaterThanOrEqualTo(postRoot.get("salary").get("min"), filter.getSalary().getMin()));
            }
            if (filter.getSalary().getMax() != null) {
                predicates.add(cb.lessThanOrEqualTo(postRoot.get("salary").get("max"), filter.getSalary().getMax()));
            }
        }

        if (!CollectionUtils.isEmpty(filter.getSkills())) {
            predicates.add(postRoot.join("skills").get("value").in(filter.getSkills()));
        }
    }

    @Transactional(readOnly = true)
    public List<Post> searchPostAdmin(SearchAdminPostRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Post> query = cb.createQuery(Post.class);
        Root<Post> postRoot = query.from(Post.class);

        List<Predicate> predicates = new ArrayList<>();

        buildSearchAdminPostPredicates(request, predicates, postRoot, cb);

        // Build the final query
        query.select(postRoot).where(predicates.toArray(new Predicate[0]));

        // Pagination
        if (request.getPage() != null && request.getPageSize() != null) {
            int offset = (request.getPage() - 1) * request.getPageSize();
            return entityManager.createQuery(query)
                    .setFirstResult(offset)
                    .setMaxResults(request.getPageSize())
                    .getResultList();
        } else {
            return entityManager.createQuery(query).getResultList();
        }
    }

    @Transactional(readOnly = true)
    public long countAdminPosts(SearchAdminPostRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Post> postRoot = countQuery.from(Post.class);

        // Using the count function
        countQuery.select(cb.count(postRoot));

        List<Predicate> predicates = new ArrayList<>();

        buildSearchAdminPostPredicates(request, predicates, postRoot, cb);

        // Apply predicates to the count query
        countQuery.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private void buildSearchAdminPostPredicates(SearchAdminPostRequest request, List<Predicate> predicates,
                                                Root<Post> postRoot, CriteriaBuilder cb) {
        if (request.getSearch() != null && !request.getSearch().trim().isEmpty()) {
            Predicate titlePredicate = cb.like(postRoot.get("title"), "%" + request.getSearch() + "%");
            Predicate desPredicate = cb.like(postRoot.get("description"), "%" + request.getSearch() + "%");
            titlePredicate = cb.or(titlePredicate, desPredicate);
            predicates.add(titlePredicate);
        }

        if (request.getStatus() != null) {
            predicates.add(postRoot.get("status").in(request.getStatus()));
        }

        if (request.getWorkType() != null) {
            predicates.add(cb.equal(
                    cb.function(
                            "jsonb_exists_any",
                            Boolean.class,
                            cb.function(
                                    "jsonb_path_query_array",
                                    String.class,
                                    postRoot.get("workingInformation"),
                                    cb.literal("$[*].workType")
                            ),
                            cb.literal(new String[]{request.getWorkType()})
                    ),
                    true
            ));
        }

        if (request.getJobType() != null) {
            predicates.add(postRoot.get("jobType").in(request.getJobType()));
        }

        if (request.getExperienceLevel() != null) {
            predicates.add(postRoot.get("experienceLevel").in(request.getExperienceLevel()));
        }

        if (request.getIsFeatureJob() != null) {
            predicates.add(cb.equal(postRoot.get("isFeatureJob"), request.getIsFeatureJob()));
        }

        if (request.getUrgentHiring() != null) {
            predicates.add(cb.equal(postRoot.get("urgentHiring"), request.getUrgentHiring()));
        }

        if (request.getDateRange() != null && request.getDateRange().size() == 2) {
            LocalDate startDate = request.getDateRange().get(0);
            LocalDate endDate = request.getDateRange().get(1);

            LocalDateTime startDateTime = startDate.atStartOfDay();

            LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

            predicates.add(cb.between(postRoot.get("postDate"), startDateTime, endDateTime));
        }

    }

}
