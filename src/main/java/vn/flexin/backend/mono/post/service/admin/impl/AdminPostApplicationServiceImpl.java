package vn.flexin.backend.mono.post.service.admin.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.response.PostApplicationResponse;
import vn.flexin.backend.mono.post.entity.Post;
import vn.flexin.backend.mono.post.entity.PostApplication;
import vn.flexin.backend.mono.post.enums.PostApplicationStatus;
import vn.flexin.backend.mono.post.repository.PostApplicationRepository;
import vn.flexin.backend.mono.post.service.PostApplicationService;
import vn.flexin.backend.mono.post.service.PostService;
import vn.flexin.backend.mono.post.service.admin.AdminPostApplicationService;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.resume.service.ResumeService;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class AdminPostApplicationServiceImpl implements AdminPostApplicationService {


    private final PostApplicationService postApplicationService;
    private final PostApplicationRepository postApplicationRepository;
    private final ResumeService resumeService;
    private final PostService postService;

    @Override
    public Pair<List<PostApplicationResponse>, PaginationResponse> searchPostApplications(PostApplicationFilter filters) {
        var postApplications = postApplicationRepository.findAll(filters);
        List<PostApplicationResponse> responses = postApplications.stream().map(PostApplicationResponse::new).toList();
        PaginationResponse paging = new PaginationResponse(filters.getLimit(), filters.getPage(), (int) postApplications.getTotalElements());
        return Pair.of(responses, paging);
    }

    @Override
    public void updatePostApplication(Long applicationId, Long resumeId) {
        PostApplication postApplication = postApplicationService.getPostApplicationById(applicationId);
        Resume currentOriginResume = resumeService.getOriginalResumeById(postApplication.getResume().getId());
        Resume currentSnapshotResume = postApplication.getResume();

        if(!postApplication.getStatus().equals(PostApplicationStatus.APPLY.getValue())) {
            throw new BadRequestException("You can only update post application when status is APPLY");
        }

        Resume updateResume = resumeService.getResumeEntityById(resumeId);

        User currentResumeUser = currentOriginResume.getUser();
        User updateResumeUser = updateResume.getUser();

        if(!updateResumeUser.getId().equals(currentResumeUser.getId())) {
            throw new AccessDeniedException("You can only update your own post application");
        }

        if(currentOriginResume.getId().equals(resumeId) && currentSnapshotResume.getLastModifiedAt().equals(updateResume.getLastModifiedAt())) {
            throw new BadRequestException("You can not update post application with the same resume");
        }

        resumeService.deleteSnapshotResume(currentSnapshotResume.getId());

        Long snapshotResumeId = resumeService.snapshotResume(resumeId);
        Resume snapshotResume = resumeService.getResumeEntityById(snapshotResumeId);

        postApplication.setResume(snapshotResume);
        postApplicationRepository.save(postApplication);
    }

    @Override
    public void deletePostApplication(Long applicationId) {
        PostApplication postApplication = postApplicationService.getPostApplicationById(applicationId);

        if(!postApplication.getStatus().equals(PostApplicationStatus.APPLY.getValue())) {
            throw new BadRequestException("You can only delete post application when status is APPLY");
        }

        postApplicationRepository.delete(postApplication);
        resumeService.deleteSnapshotResume(postApplication.getResume().getId());
    }

    @Override
    public Long createPostApplication(Long postId, Long resumeId) {
        Post post = postService.getById(postId);
        Resume resume = resumeService.getResumeEntityById(resumeId);

        User resumeUser = resume.getUser();

        var existPostApplication = postApplicationRepository.findPostApplicationByUserIdAndPostId(resumeUser.getId(), postId);
        if (existPostApplication.isPresent()) {
            throw new BadRequestException("User already applied for this position");
        }

        Long snapshotResumeId = resumeService.snapshotResume(resumeId);
        Resume snapshotResume = resumeService.getResumeEntityById(snapshotResumeId);

        PostApplication postApplication = new PostApplication();
        postApplication.setPost(post);
        postApplication.setResume(snapshotResume);
        postApplication.setStatus(PostApplicationStatus.APPLY.getValue());
        postApplicationRepository.save(postApplication);
        return postApplication.getId();
    }

    @Override
    public List<PostApplicationResponse> getPostApplicationsByPostId(Long postId) {
        List<PostApplication> postApplications = postApplicationRepository.findAllByPostId(postId);
        return postApplications.stream().map(PostApplicationResponse::new).toList();
    }
}
